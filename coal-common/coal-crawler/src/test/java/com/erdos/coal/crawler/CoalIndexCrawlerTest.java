package com.erdos.coal.crawler;

import com.erdos.coal.crawler.dto.CoalIndexCrawlRequest;
import com.erdos.coal.crawler.dto.CoalIndexCrawlResponse;
import com.erdos.coal.crawler.enums.IndexType;
import com.erdos.coal.crawler.service.CoalIndexCrawlerService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * 煤炭指数爬虫测试类
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class CoalIndexCrawlerTest {
    
    @Autowired
    private CoalIndexCrawlerService coalIndexCrawlerService;
    
    @Test
    public void testCrawlCCIData() {
        CoalIndexCrawlRequest request = new CoalIndexCrawlRequest(IndexType.CCI);
        request.setForceRefresh(true);
        request.setTimeout(60000L);
        
        CoalIndexCrawlResponse response = coalIndexCrawlerService.crawlCoalIndexData(request);
        
        System.out.println("爬取结果: " + response.getSuccess());
        System.out.println("数据条数: " + response.getDataCount());
        System.out.println("耗时: " + response.getDuration() + "ms");
        
        if (!response.getSuccess()) {
            System.out.println("错误信息: " + response.getErrorMessage());
        }
    }
    
    @Test
    public void testCrawlCCTDData() {
        CoalIndexCrawlRequest request = new CoalIndexCrawlRequest(IndexType.CCTD);
        request.setForceRefresh(true);
        
        CoalIndexCrawlResponse response = coalIndexCrawlerService.crawlCoalIndexData(request);
        
        System.out.println("爬取结果: " + response.getSuccess());
        System.out.println("数据条数: " + response.getDataCount());
        System.out.println("耗时: " + response.getDuration() + "ms");
    }
    
    @Test
    public void testCrawlShenhuaData() {
        CoalIndexCrawlRequest request = new CoalIndexCrawlRequest(IndexType.SHENHUA);
        request.setForceRefresh(true);
        
        CoalIndexCrawlResponse response = coalIndexCrawlerService.crawlCoalIndexData(request);
        
        System.out.println("爬取结果: " + response.getSuccess());
        System.out.println("数据条数: " + response.getDataCount());
        System.out.println("耗时: " + response.getDuration() + "ms");
    }
    
    @Test
    public void testCrawlAllData() {
        CoalIndexCrawlResponse response = coalIndexCrawlerService.crawlAllLatestData();
        
        System.out.println("爬取结果: " + response.getSuccess());
        System.out.println("总数据条数: " + response.getDataCount());
        System.out.println("总耗时: " + response.getDuration() + "ms");
        
        if (response.getDataList() != null) {
            response.getDataList().forEach(data -> {
                System.out.println("指数类型: " + data.getIndexType() + 
                                 ", 热值: " + data.getCalorificValue() + 
                                 ", 价格: " + data.getPrice());
            });
        }
    }
}
