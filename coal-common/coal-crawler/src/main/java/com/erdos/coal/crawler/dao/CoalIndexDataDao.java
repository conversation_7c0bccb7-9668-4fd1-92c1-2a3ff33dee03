package com.erdos.coal.crawler.dao;

import com.erdos.coal.crawler.entity.CoalIndexData;
import com.erdos.coal.crawler.enums.IndexType;

import java.util.Date;
import java.util.List;

/**
 * 煤炭指数数据DAO接口
 */
public interface CoalIndexDataDao {
    
    /**
     * 保存煤炭指数数据
     */
    void save(CoalIndexData data);
    
    /**
     * 批量保存煤炭指数数据
     */
    void saveBatch(List<CoalIndexData> dataList);
    
    /**
     * 根据指数类型、日期、热值查询数据
     */
    CoalIndexData findByTypeAndDateAndCaloric(IndexType indexType, Date dataDate, Integer calorificValue);
    
    /**
     * 根据指数类型和日期范围查询数据
     */
    List<CoalIndexData> findByTypeAndDateRange(IndexType indexType, Date startDate, Date endDate);
    
    /**
     * 根据指数类型查询最新数据
     */
    List<CoalIndexData> findLatestByType(IndexType indexType);
    
    /**
     * 根据指数类型和热值查询最新数据
     */
    CoalIndexData findLatestByTypeAndCaloric(IndexType indexType, Integer calorificValue);
    
    /**
     * 根据指数类型查询历史数据（用于计算涨跌幅）
     */
    List<CoalIndexData> findHistoryByType(IndexType indexType, int limit);
    
    /**
     * 更新数据
     */
    void update(CoalIndexData data);
    
    /**
     * 删除数据
     */
    void delete(String id);
    
    /**
     * 根据ID查询数据
     */
    CoalIndexData findById(String id);
    
    /**
     * 统计指定条件的数据数量
     */
    long countByTypeAndDateRange(IndexType indexType, Date startDate, Date endDate);
}
