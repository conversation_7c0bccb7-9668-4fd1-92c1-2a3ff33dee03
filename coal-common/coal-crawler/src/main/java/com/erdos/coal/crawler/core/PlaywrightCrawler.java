package com.erdos.coal.crawler.core;

import com.erdos.coal.crawler.dto.CoalIndexDataDto;
import com.erdos.coal.crawler.enums.IndexType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.microsoft.playwright.*;
import com.microsoft.playwright.options.LoadState;
import com.microsoft.playwright.options.WaitForSelectorState;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Playwright爬虫核心类
 */
@Component
public class PlaywrightCrawler {
    
    private static final Logger logger = LoggerFactory.getLogger(PlaywrightCrawler.class);
    
    private static final String BASE_URL = "https://www.meiyibao.com/";
    private static final int DEFAULT_TIMEOUT = 30000;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 爬取煤炭指数数据
     */
    public List<CoalIndexDataDto> crawlCoalIndexData(IndexType indexType, Long timeout) {
        List<CoalIndexDataDto> resultList = new ArrayList<>();
        
        try (Playwright playwright = Playwright.create()) {
            Browser browser = playwright.chromium().launch(new BrowserType.LaunchOptions()
                    .setHeadless(true)
                    .setTimeout(timeout != null ? timeout.doubleValue() : DEFAULT_TIMEOUT));
            
            Page page = browser.newPage();
            
            // 设置页面超时
            page.setDefaultTimeout(timeout != null ? timeout.doubleValue() : DEFAULT_TIMEOUT);
            
            // 访问网站
            page.navigate(BASE_URL);
            
            // 等待页面加载完成
            page.waitForLoadState(LoadState.NETWORKIDLE);
            
            // 根据指数类型点击对应的标签
            clickIndexTab(page, indexType);
            
            // 等待图表加载
            Thread.sleep(2000);
            
            // 爬取左侧文本数据
            List<CoalIndexDataDto> textData = crawlTextData(page, indexType);
            resultList.addAll(textData);
            
            // 爬取图表数据
            List<CoalIndexDataDto> chartData = crawlChartData(page, indexType);
            resultList.addAll(chartData);
            
            browser.close();
            
        } catch (Exception e) {
            logger.error("爬取煤炭指数数据失败: {}", e.getMessage(), e);
            throw new RuntimeException("爬取数据失败: " + e.getMessage(), e);
        }
        
        return resultList;
    }
    
    /**
     * 点击指数类型标签
     */
    private void clickIndexTab(Page page, IndexType indexType) {
        try {
            String tabText = getTabText(indexType);
            
            // 查找并点击对应的标签
            Locator tabLocator = page.locator("text=" + tabText);
            if (tabLocator.count() > 0) {
                tabLocator.first().click();
                logger.info("成功点击{}标签", tabText);
                
                // 等待内容加载
                Thread.sleep(1000);
            } else {
                logger.warn("未找到{}标签", tabText);
            }
        } catch (Exception e) {
            logger.error("点击标签失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 获取标签文本
     */
    private String getTabText(IndexType indexType) {
        switch (indexType) {
            case SHENHUA:
                return "神华外购";
            case CCI:
                return "CCI指数";
            case CCTD:
                return "CCTD指数";
            default:
                return "CCI指数";
        }
    }
    
    /**
     * 爬取左侧文本数据
     */
    private List<CoalIndexDataDto> crawlTextData(Page page, IndexType indexType) {
        List<CoalIndexDataDto> dataList = new ArrayList<>();
        
        try {
            // 查找价格卡片
            Locator priceCards = page.locator(".price-card, .index-card, [class*='price'], [class*='index']");
            
            for (int i = 0; i < priceCards.count(); i++) {
                Locator card = priceCards.nth(i);
                
                try {
                    String cardText = card.textContent();
                    logger.debug("卡片文本: {}", cardText);
                    
                    CoalIndexDataDto data = parseTextData(cardText, indexType);
                    if (data != null) {
                        dataList.add(data);
                    }
                } catch (Exception e) {
                    logger.warn("解析卡片数据失败: {}", e.getMessage());
                }
            }
            
            // 如果没有找到卡片，尝试其他选择器
            if (dataList.isEmpty()) {
                dataList.addAll(crawlTextDataAlternative(page, indexType));
            }
            
        } catch (Exception e) {
            logger.error("爬取文本数据失败: {}", e.getMessage(), e);
        }
        
        return dataList;
    }
    
    /**
     * 备用文本数据爬取方法
     */
    private List<CoalIndexDataDto> crawlTextDataAlternative(Page page, IndexType indexType) {
        List<CoalIndexDataDto> dataList = new ArrayList<>();
        
        try {
            // 尝试通过页面内容匹配数据
            String pageContent = page.content();
            
            // 正则匹配价格和热值信息
            Pattern pattern = Pattern.compile("(\\d+)元.*?(\\d+)kCal");
            Matcher matcher = pattern.matcher(pageContent);
            
            while (matcher.find()) {
                try {
                    BigDecimal price = new BigDecimal(matcher.group(1));
                    Integer calorificValue = Integer.parseInt(matcher.group(2));
                    
                    CoalIndexDataDto data = new CoalIndexDataDto();
                    data.setIndexType(indexType);
                    data.setPrice(price);
                    data.setCalorificValue(calorificValue);
                    data.setDataDate(new Date());
                    data.setSourceUrl(BASE_URL);
                    
                    dataList.add(data);
                } catch (Exception e) {
                    logger.warn("解析匹配数据失败: {}", e.getMessage());
                }
            }
            
        } catch (Exception e) {
            logger.error("备用文本数据爬取失败: {}", e.getMessage(), e);
        }
        
        return dataList;
    }
    
    /**
     * 解析文本数据
     */
    private CoalIndexDataDto parseTextData(String text, IndexType indexType) {
        try {
            // 提取价格
            Pattern pricePattern = Pattern.compile("(\\d+)元");
            Matcher priceMatcher = pricePattern.matcher(text);
            
            // 提取热值
            Pattern calorificPattern = Pattern.compile("(\\d+)kCal");
            Matcher calorificMatcher = calorificPattern.matcher(text);
            
            // 提取涨跌
            Pattern changePattern = Pattern.compile("([+\\-]?\\d+)");
            Matcher changeMatcher = changePattern.matcher(text);
            
            if (priceMatcher.find() && calorificMatcher.find()) {
                CoalIndexDataDto data = new CoalIndexDataDto();
                data.setIndexType(indexType);
                data.setPrice(new BigDecimal(priceMatcher.group(1)));
                data.setCalorificValue(Integer.parseInt(calorificMatcher.group(1)));
                data.setDataDate(new Date());
                data.setSourceUrl(BASE_URL);
                
                // 设置涨跌信息
                if (changeMatcher.find()) {
                    BigDecimal changeAmount = new BigDecimal(changeMatcher.group(1));
                    data.setChangeAmount(changeAmount);
                    data.setTrendDirection(changeAmount.compareTo(BigDecimal.ZERO) > 0 ? "上涨" : 
                                         changeAmount.compareTo(BigDecimal.ZERO) < 0 ? "下跌" : "持平");
                }
                
                return data;
            }
        } catch (Exception e) {
            logger.warn("解析文本数据失败: {}", e.getMessage());
        }
        
        return null;
    }
    
    /**
     * 爬取图表数据
     */
    private List<CoalIndexDataDto> crawlChartData(Page page, IndexType indexType) {
        List<CoalIndexDataDto> dataList = new ArrayList<>();
        
        try {
            // 等待图表加载
            page.waitForSelector("canvas, svg, .chart", new Page.WaitForSelectorOptions()
                    .setState(WaitForSelectorState.VISIBLE)
                    .setTimeout(5000));
            
            // 执行JavaScript获取图表数据
            Object chartDataObj = page.evaluate("""
                () => {
                    // 尝试获取ECharts实例
                    const charts = [];
                    if (window.echarts) {
                        const instances = window.echarts.getInstanceByDom ? 
                            [window.echarts.getInstanceByDom(document.querySelector('canvas'))] :
                            Object.values(window.echarts._instances || {});
                        
                        instances.forEach(chart => {
                            if (chart && chart.getOption) {
                                const option = chart.getOption();
                                if (option.series && option.series.length > 0) {
                                    charts.push({
                                        series: option.series,
                                        xAxis: option.xAxis,
                                        yAxis: option.yAxis
                                    });
                                }
                            }
                        });
                    }
                    
                    return charts;
                }
                """);
            
            if (chartDataObj != null) {
                dataList.addAll(parseChartData(chartDataObj.toString(), indexType));
            }
            
        } catch (Exception e) {
            logger.error("爬取图表数据失败: {}", e.getMessage(), e);
        }
        
        return dataList;
    }
    
    /**
     * 解析图表数据
     */
    private List<CoalIndexDataDto> parseChartData(String chartDataJson, IndexType indexType) {
        List<CoalIndexDataDto> dataList = new ArrayList<>();
        
        try {
            JsonNode chartData = objectMapper.readTree(chartDataJson);
            
            if (chartData.isArray() && chartData.size() > 0) {
                JsonNode firstChart = chartData.get(0);
                JsonNode series = firstChart.get("series");
                JsonNode xAxis = firstChart.get("xAxis");
                
                if (series != null && series.isArray()) {
                    for (JsonNode seriesItem : series) {
                        JsonNode data = seriesItem.get("data");
                        String name = seriesItem.get("name").asText();
                        
                        if (data != null && data.isArray()) {
                            for (int i = 0; i < data.size(); i++) {
                                try {
                                    CoalIndexDataDto dto = parseSeriesDataPoint(data.get(i), name, indexType, i, xAxis);
                                    if (dto != null) {
                                        dataList.add(dto);
                                    }
                                } catch (Exception e) {
                                    logger.warn("解析数据点失败: {}", e.getMessage());
                                }
                            }
                        }
                    }
                }
            }
            
        } catch (Exception e) {
            logger.error("解析图表JSON数据失败: {}", e.getMessage(), e);
        }
        
        return dataList;
    }
    
    /**
     * 解析单个数据点
     */
    private CoalIndexDataDto parseSeriesDataPoint(JsonNode dataPoint, String seriesName, 
                                                 IndexType indexType, int index, JsonNode xAxis) {
        try {
            CoalIndexDataDto data = new CoalIndexDataDto();
            data.setIndexType(indexType);
            data.setSourceUrl(BASE_URL);
            
            // 解析价格
            if (dataPoint.isNumber()) {
                data.setPrice(new BigDecimal(dataPoint.asText()));
            } else if (dataPoint.isArray() && dataPoint.size() >= 2) {
                data.setPrice(new BigDecimal(dataPoint.get(1).asText()));
            }
            
            // 解析热值（从系列名称中提取）
            Pattern calorificPattern = Pattern.compile("(\\d+)kCal");
            Matcher matcher = calorificPattern.matcher(seriesName);
            if (matcher.find()) {
                data.setCalorificValue(Integer.parseInt(matcher.group(1)));
            }
            
            // 解析日期（从x轴数据中获取）
            if (xAxis != null && xAxis.isArray() && xAxis.size() > 0) {
                JsonNode xAxisData = xAxis.get(0).get("data");
                if (xAxisData != null && xAxisData.isArray() && index < xAxisData.size()) {
                    String dateStr = xAxisData.get(index).asText();
                    data.setDataDate(parseDate(dateStr));
                }
            }
            
            if (data.getDataDate() == null) {
                data.setDataDate(new Date());
            }
            
            return data;
            
        } catch (Exception e) {
            logger.warn("解析数据点失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 解析日期字符串
     */
    private Date parseDate(String dateStr) {
        try {
            // 尝试多种日期格式
            String[] patterns = {
                "MM-dd", "yyyy-MM-dd", "MM/dd", "yyyy/MM/dd", 
                "MM月dd日", "yyyy年MM月dd日"
            };
            
            for (String pattern : patterns) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat(pattern);
                    Date date = sdf.parse(dateStr);
                    
                    // 如果只有月日，补充年份
                    if (pattern.equals("MM-dd") || pattern.equals("MM/dd") || pattern.equals("MM月dd日")) {
                        Calendar cal = Calendar.getInstance();
                        cal.setTime(date);
                        cal.set(Calendar.YEAR, Calendar.getInstance().get(Calendar.YEAR));
                        date = cal.getTime();
                    }
                    
                    return date;
                } catch (ParseException ignored) {
                    // 继续尝试下一个格式
                }
            }
        } catch (Exception e) {
            logger.warn("解析日期失败: {}", e.getMessage());
        }
        
        return new Date(); // 默认返回当前日期
    }
}
