package com.erdos.coal.crawler.dao;

import com.erdos.coal.crawler.entity.CoalCrawlLog;
import com.erdos.coal.crawler.enums.IndexType;

import java.util.Date;
import java.util.List;

/**
 * 煤炭爬虫日志DAO接口
 */
public interface CoalCrawlLogDao {
    
    /**
     * 保存爬虫日志
     */
    void save(CoalCrawlLog log);
    
    /**
     * 根据ID查询日志
     */
    CoalCrawlLog findById(String id);
    
    /**
     * 根据指数类型查询最近的日志
     */
    List<CoalCrawlLog> findRecentByType(IndexType indexType, int limit);
    
    /**
     * 根据日期范围查询日志
     */
    List<CoalCrawlLog> findByDateRange(Date startDate, Date endDate);
    
    /**
     * 查询成功的爬虫日志
     */
    List<CoalCrawlLog> findSuccessLogs(IndexType indexType, int limit);
    
    /**
     * 查询失败的爬虫日志
     */
    List<CoalCrawlLog> findFailedLogs(IndexType indexType, int limit);
    
    /**
     * 更新日志状态
     */
    void updateStatus(String id, Integer status, String errorMessage);
}
