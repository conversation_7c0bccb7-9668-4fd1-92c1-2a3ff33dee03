package com.erdos.coal.crawler;

import com.erdos.coal.crawler.core.PlaywrightCrawler;
import com.erdos.coal.crawler.dto.CoalIndexDataDto;
import com.erdos.coal.crawler.enums.IndexType;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;

import java.util.List;

/**
 * 煤炭爬虫测试应用
 * 用于快速测试爬虫功能，不启动Web服务
 */
@SpringBootApplication(
    exclude = {SecurityAutoConfiguration.class},
    scanBasePackages = {"com.erdos.coal.crawler"}
)
public class CrawlerTestApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(CrawlerTestApplication.class, args);
    }
    
    @Bean
    public CommandLineRunner testCrawler() {
        return args -> {
            System.out.println("=== 开始测试爬虫功能 ===");
            
            try {
                PlaywrightCrawler crawler = new PlaywrightCrawler();
                
                // 测试爬取CCI指数
                System.out.println("测试爬取CCI指数...");
                List<CoalIndexDataDto> cciData = crawler.crawlCoalIndexData(IndexType.CCI, 30000L);
                System.out.println("CCI数据条数: " + cciData.size());
                
                // 测试爬取CCTD指数
                System.out.println("测试爬取CCTD指数...");
                List<CoalIndexDataDto> cctdData = crawler.crawlCoalIndexData(IndexType.CCTD, 30000L);
                System.out.println("CCTD数据条数: " + cctdData.size());
                
                // 测试爬取神华外购
                System.out.println("测试爬取神华外购...");
                List<CoalIndexDataDto> shenhuaData = crawler.crawlCoalIndexData(IndexType.SHENHUA, 30000L);
                System.out.println("神华数据条数: " + shenhuaData.size());
                
                System.out.println("=== 爬虫功能测试完成 ===");
                
            } catch (Exception e) {
                System.err.println("爬虫测试失败: " + e.getMessage());
                e.printStackTrace();
            }
        };
    }
}
