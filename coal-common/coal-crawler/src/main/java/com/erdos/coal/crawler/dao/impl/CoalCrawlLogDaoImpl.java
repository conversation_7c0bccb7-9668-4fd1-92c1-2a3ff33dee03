package com.erdos.coal.crawler.dao.impl;

import com.erdos.coal.core.base.mongo.impl.BaseMongoDAOImpl;
import com.erdos.coal.crawler.dao.CoalCrawlLogDao;
import com.erdos.coal.crawler.entity.CoalCrawlLog;
import com.erdos.coal.crawler.enums.IndexType;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import dev.morphia.query.UpdateOperations;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 煤炭爬虫日志DAO实现类
 */
@Repository
public class CoalCrawlLogDaoImpl extends BaseMongoDAOImpl<CoalCrawlLog> implements CoalCrawlLogDao {
    
    @Override
    public void save(CoalCrawlLog log) {
        super.save(log);
    }
    
    @Override
    public CoalCrawlLog findById(String id) {
        return super.get(id);
    }
    
    @Override
    public List<CoalCrawlLog> findRecentByType(IndexType indexType, int limit) {
        Query<CoalCrawlLog> query = createQuery()
                .field("indexType").equal(indexType)
                .field("status").equal(1)
                .order(Sort.descending("createTime"))
                .limit(limit);
        return query.asList();
    }
    
    @Override
    public List<CoalCrawlLog> findByDateRange(Date startDate, Date endDate) {
        Query<CoalCrawlLog> query = createQuery()
                .field("status").equal(1);
        
        if (startDate != null) {
            query.field("createTime").greaterThanOrEq(startDate);
        }
        if (endDate != null) {
            query.field("createTime").lessThanOrEq(endDate);
        }
        
        return query.order(Sort.descending("createTime")).asList();
    }
    
    @Override
    public List<CoalCrawlLog> findSuccessLogs(IndexType indexType, int limit) {
        Query<CoalCrawlLog> query = createQuery()
                .field("indexType").equal(indexType)
                .field("crawlStatus").equal(1)
                .field("status").equal(1)
                .order(Sort.descending("createTime"))
                .limit(limit);
        return query.asList();
    }
    
    @Override
    public List<CoalCrawlLog> findFailedLogs(IndexType indexType, int limit) {
        Query<CoalCrawlLog> query = createQuery()
                .field("indexType").equal(indexType)
                .field("crawlStatus").equal(0)
                .field("status").equal(1)
                .order(Sort.descending("createTime"))
                .limit(limit);
        return query.asList();
    }
    
    @Override
    public void updateStatus(String id, Integer crawlStatus, String errorMessage) {
        Query<CoalCrawlLog> query = createQuery().field("objectId").equal(id);
        UpdateOperations<CoalCrawlLog> updateOps = createUpdateOperations()
                .set("crawlStatus", crawlStatus)
                .set("updateTime", System.currentTimeMillis());
        
        if (errorMessage != null) {
            updateOps.set("errorMessage", errorMessage);
        }
        
        super.update(query, updateOps);
    }
}
