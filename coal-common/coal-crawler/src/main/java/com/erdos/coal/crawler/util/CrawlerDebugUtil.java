package com.erdos.coal.crawler.util;

import com.erdos.coal.crawler.constants.JavaScriptConstants;
import com.microsoft.playwright.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 爬虫调试工具类
 * 用于调试和测试爬虫功能
 */
public class CrawlerDebugUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(CrawlerDebugUtil.class);
    private static final String BASE_URL = "https://www.meiyibao.com/";
    
    /**
     * 调试页面元素
     */
    public static void debugPageElements() {
        try (Playwright playwright = Playwright.create()) {
            // 启动浏览器（可视化模式用于调试）
            Browser browser = playwright.chromium().launch(
                    new BrowserType.LaunchOptions()
                            .setHeadless(false) // 设置为false以便观察
                            .setSlowMo(1000) // 慢动作，便于观察
            );
            
            // 创建浏览器上下文
            BrowserContext context = browser.newContext(
                    new Browser.NewContextOptions()
                            .setUserAgent("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36")
            );

            // 创建新页面
            Page page = context.newPage();
            
            System.out.println("正在访问: " + BASE_URL);
            page.navigate(BASE_URL);
            
            // 等待页面加载
            System.out.println("等待页面加载...");
            page.waitForSelector(".coal-ing", new Page.WaitForSelectorOptions().setTimeout(30_000));
            
            // 测试各个标签页
            testTabSwitching(page);
            
            // 获取页面元素信息
            analyzePageElements(page);
            
            System.out.println("调试完成，按回车键关闭浏览器...");
            System.in.read(); // 等待用户输入
            
            page.close();
            context.close();
            browser.close();
            
        } catch (Exception e) {
            logger.error("调试失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 测试标签页切换
     */
    private static void testTabSwitching(Page page) {
        try {
            System.out.println("\n=== 测试标签页切换 ===");
            
            // 测试点击各个标签
            String[] tabs = {"#tab-1", "#tab-2", "#tab-3"};
            String[] tabNames = {"神华外购", "CCI指数", "CCTD指数"};
            
            for (int i = 0; i < tabs.length; i++) {
                try {
                    System.out.println("点击标签: " + tabNames[i] + " (" + tabs[i] + ")");
                    page.querySelector(tabs[i]).click();
                    
                    // 等待内容加载
                    page.waitForTimeout(2000);
                    
                    // 获取当前标签页的内容
                    Locator coalIngElements = page.locator(".coal-ing");
                    int count = coalIngElements.count();
                    System.out.println("找到 " + count + " 个 coal-ing 元素");
                    
                    for (int j = 0; j < Math.min(count, 3); j++) {
                        String text = coalIngElements.nth(j).textContent();
                        System.out.println("  元素[" + j + "]: " + text.substring(0, Math.min(text.length(), 100)) + "...");
                    }
                    
                } catch (Exception e) {
                    System.err.println("测试标签 " + tabs[i] + " 失败: " + e.getMessage());
                }
            }
            
        } catch (Exception e) {
            logger.error("测试标签页切换失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 分析页面元素
     */
    private static void analyzePageElements(Page page) {
        try {
            System.out.println("\n=== 分析页面元素 ===");
            
            // 分析coal-ing元素
            analyzeCoalIngElements(page);
            
            // 分析其他可能的价格元素
            analyzeOtherPriceElements(page);
            
            // 分析图表元素
            analyzeChartElements(page);
            
        } catch (Exception e) {
            logger.error("分析页面元素失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 分析coal-ing元素
     */
    private static void analyzeCoalIngElements(Page page) {
        try {
            System.out.println("\n--- coal-ing 元素分析 ---");
            
            Locator coalIngElements = page.locator(".coal-ing");
            int count = coalIngElements.count();
            System.out.println("总共找到 " + count + " 个 coal-ing 元素");
            
            for (int i = 0; i < count; i++) {
                try {
                    Locator element = coalIngElements.nth(i);
                    String text = element.textContent();
                    String html = element.innerHTML();
                    
                    System.out.println("\n元素[" + i + "]:");
                    System.out.println("  文本内容: " + text);
                    System.out.println("  HTML长度: " + html.length());
                    
                    // 尝试提取价格和热值信息
                    if (text.contains("元") && text.contains("kCal")) {
                        System.out.println("  ✓ 包含价格和热值信息");
                    }
                    
                } catch (Exception e) {
                    System.err.println("  分析元素[" + i + "]失败: " + e.getMessage());
                }
            }
            
        } catch (Exception e) {
            logger.error("分析coal-ing元素失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 分析其他价格元素
     */
    private static void analyzeOtherPriceElements(Page page) {
        try {
            System.out.println("\n--- 其他价格元素分析 ---");
            
            String[] selectors = {
                ".price-card", ".index-card", ".coal-price", ".price-info",
                "[class*='price']", "[class*='coal']", "[class*='index']"
            };
            
            for (String selector : selectors) {
                try {
                    Locator elements = page.locator(selector);
                    int count = elements.count();
                    if (count > 0) {
                        System.out.println("选择器 " + selector + " 找到 " + count + " 个元素");
                    }
                } catch (Exception e) {
                    // 忽略选择器错误
                }
            }
            
        } catch (Exception e) {
            logger.error("分析其他价格元素失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 分析图表元素
     */
    private static void analyzeChartElements(Page page) {
        try {
            System.out.println("\n--- 图表元素分析 ---");
            
            // 检查canvas元素
            Locator canvasElements = page.locator("canvas");
            System.out.println("找到 " + canvasElements.count() + " 个 canvas 元素");
            
            // 检查svg元素
            Locator svgElements = page.locator("svg");
            System.out.println("找到 " + svgElements.count() + " 个 svg 元素");
            
            // 检查ECharts
            try {
                Object echartsInfo = page.evaluate(JavaScriptConstants.CHECK_ECHARTS_AVAILABLE);
                System.out.println("ECharts 信息: " + echartsInfo);
            } catch (Exception e) {
                System.err.println("ECharts检查失败: " + e.getMessage());
            }

            // 尝试获取图表数据
            try {
                Object chartData = page.evaluate(JavaScriptConstants.GET_ECHARTS_DATA);
                System.out.println("图表数据: " + chartData);
            } catch (Exception e) {
                System.err.println("图表数据获取失败: " + e.getMessage());
            }
            
        } catch (Exception e) {
            logger.error("分析图表元素失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 主方法，用于独立运行调试
     */
    public static void main(String[] args) {
        System.out.println("开始调试煤炭爬虫...");
        debugPageElements();
        System.out.println("调试结束");
    }
}
