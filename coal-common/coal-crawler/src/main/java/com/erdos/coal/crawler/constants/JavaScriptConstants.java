package com.erdos.coal.crawler.constants;

/**
 * JavaScript代码常量类
 * 用于存储在页面中执行的JavaScript代码
 */
public class JavaScriptConstants {
    
    /**
     * 获取ECharts图表数据的JavaScript代码
     */
    public static final String GET_ECHARTS_DATA = 
            "() => {" +
            "    const charts = [];" +
            "    try {" +
            "        if (window.echarts) {" +
            "            let instances = [];" +
            "            " +
            "            // 尝试不同的方式获取ECharts实例" +
            "            if (window.echarts.getInstanceByDom) {" +
            "                const canvases = document.querySelectorAll('canvas');" +
            "                canvases.forEach(canvas => {" +
            "                    const instance = window.echarts.getInstanceByDom(canvas);" +
            "                    if (instance) instances.push(instance);" +
            "                });" +
            "            }" +
            "            " +
            "            // 备用方法：通过_instances获取" +
            "            if (instances.length === 0 && window.echarts._instances) {" +
            "                instances = Object.values(window.echarts._instances);" +
            "            }" +
            "            " +
            "            // 提取图表数据" +
            "            instances.forEach(chart => {" +
            "                if (chart && chart.getOption) {" +
            "                    try {" +
            "                        const option = chart.getOption();" +
            "                        if (option && option.series && option.series.length > 0) {" +
            "                            charts.push({" +
            "                                series: option.series," +
            "                                xAxis: option.xAxis," +
            "                                yAxis: option.yAxis," +
            "                                legend: option.legend," +
            "                                title: option.title" +
            "                            });" +
            "                        }" +
            "                    } catch (e) {" +
            "                        console.warn('提取图表选项失败:', e);" +
            "                    }" +
            "                }" +
            "            });" +
            "        }" +
            "    } catch (e) {" +
            "        console.error('获取ECharts数据失败:', e);" +
            "    }" +
            "    " +
            "    return charts;" +
            "}";
    
    /**
     * 检查页面是否加载完成的JavaScript代码
     */
    public static final String CHECK_PAGE_READY = 
            "() => {" +
            "    return document.readyState === 'complete' && " +
            "           document.querySelector('.coal-ing') !== null;" +
            "}";
    
    /**
     * 获取页面中所有价格相关元素的JavaScript代码
     */
    public static final String GET_PRICE_ELEMENTS = 
            "() => {" +
            "    const elements = [];" +
            "    const selectors = [" +
            "        '.coal-ing'," +
            "        '.price-card'," +
            "        '.index-card'," +
            "        '.coal-price'," +
            "        '[class*=\"price\"]'," +
            "        '[class*=\"coal\"]'," +
            "        '[class*=\"index\"]'" +
            "    ];" +
            "    " +
            "    selectors.forEach(selector => {" +
            "        try {" +
            "            const nodeList = document.querySelectorAll(selector);" +
            "            nodeList.forEach((element, index) => {" +
            "                elements.push({" +
            "                    selector: selector," +
            "                    index: index," +
            "                    text: element.textContent || ''," +
            "                    html: element.innerHTML || ''," +
            "                    className: element.className || ''" +
            "                });" +
            "            });" +
            "        } catch (e) {" +
            "            console.warn('选择器失败:', selector, e);" +
            "        }" +
            "    });" +
            "    " +
            "    return elements;" +
            "}";
    
    /**
     * 检查ECharts是否可用的JavaScript代码
     */
    public static final String CHECK_ECHARTS_AVAILABLE = 
            "() => {" +
            "    return {" +
            "        available: typeof window.echarts !== 'undefined'," +
            "        version: window.echarts ? window.echarts.version : null," +
            "        instanceCount: window.echarts && window.echarts._instances ? " +
            "                      Object.keys(window.echarts._instances).length : 0" +
            "    };" +
            "}";
    
    /**
     * 获取当前标签页信息的JavaScript代码
     */
    public static final String GET_CURRENT_TAB_INFO = 
            "() => {" +
            "    const tabs = document.querySelectorAll('[id^=\"tab-\"]');" +
            "    const activeTab = document.querySelector('.active, .selected, [class*=\"active\"]');" +
            "    " +
            "    return {" +
            "        totalTabs: tabs.length," +
            "        activeTabId: activeTab ? activeTab.id : null," +
            "        activeTabText: activeTab ? activeTab.textContent : null," +
            "        allTabIds: Array.from(tabs).map(tab => tab.id)" +
            "    };" +
            "}";
    
    /**
     * 等待特定元素出现的JavaScript代码
     */
    public static String waitForElement(String selector, int timeoutMs) {
        return "() => {" +
               "    return new Promise((resolve, reject) => {" +
               "        const timeout = " + timeoutMs + ";" +
               "        const interval = 100;" +
               "        let elapsed = 0;" +
               "        " +
               "        const check = () => {" +
               "            const element = document.querySelector('" + selector + "');" +
               "            if (element) {" +
               "                resolve(true);" +
               "            } else if (elapsed >= timeout) {" +
               "                reject(new Error('元素等待超时: " + selector + "'));" +
               "            } else {" +
               "                elapsed += interval;" +
               "                setTimeout(check, interval);" +
               "            }" +
               "        };" +
               "        " +
               "        check();" +
               "    });" +
               "}";
    }
    
    /**
     * 模拟鼠标悬停获取图表数据的JavaScript代码
     */
    public static final String SIMULATE_CHART_HOVER = 
            "() => {" +
            "    const results = [];" +
            "    try {" +
            "        const canvases = document.querySelectorAll('canvas');" +
            "        canvases.forEach((canvas, index) => {" +
            "            try {" +
            "                const rect = canvas.getBoundingClientRect();" +
            "                const centerX = rect.left + rect.width / 2;" +
            "                const centerY = rect.top + rect.height / 2;" +
            "                " +
            "                // 创建鼠标事件" +
            "                const event = new MouseEvent('mousemove', {" +
            "                    clientX: centerX," +
            "                    clientY: centerY," +
            "                    bubbles: true" +
            "                });" +
            "                " +
            "                canvas.dispatchEvent(event);" +
            "                " +
            "                // 等待一下，然后检查是否有tooltip出现" +
            "                setTimeout(() => {" +
            "                    const tooltips = document.querySelectorAll('.echarts-tooltip, [class*=\"tooltip\"]');" +
            "                    tooltips.forEach(tooltip => {" +
            "                        if (tooltip.style.display !== 'none') {" +
            "                            results.push({" +
            "                                canvasIndex: index," +
            "                                tooltipText: tooltip.textContent || tooltip.innerText" +
            "                            });" +
            "                        }" +
            "                    });" +
            "                }, 500);" +
            "                " +
            "            } catch (e) {" +
            "                console.warn('模拟悬停失败:', e);" +
            "            }" +
            "        });" +
            "    } catch (e) {" +
            "        console.error('模拟图表悬停失败:', e);" +
            "    }" +
            "    " +
            "    return results;" +
            "}";
}
