// package com.erdos.coal.crawler;
//
// import org.springframework.boot.SpringApplication;
// import org.springframework.boot.autoconfigure.SpringBootApplication;
// import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
// import org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration;
// import org.springframework.context.annotation.ComponentScan;
// import org.springframework.context.annotation.FilterType;
// import org.springframework.scheduling.annotation.EnableScheduling;
//
// /**
//  * 煤炭爬虫独立应用启动类
//  * 完全独立运行，不依赖其他模块的安全配置
//  */
// @SpringBootApplication(
//     exclude = {
//         SecurityAutoConfiguration.class,
//         UserDetailsServiceAutoConfiguration.class
//     }
// )
// @ComponentScan(
//     basePackages = {
//         "com.erdos.coal.crawler",
//         "com.erdos.coal.core.base",
//         "com.erdos.coal.core.ds.mongo"
//     },
//     excludeFilters = {
//         @ComponentScan.Filter(
//             type = FilterType.REGEX,
//             pattern = "com\\.erdos\\.coal\\.core\\.security\\..*"
//         )
//     }
// )
// @EnableScheduling
// public class CrawlerStandaloneApplication {
//
//     public static void main(String[] args) {
//         // 设置系统属性，禁用安全相关功能
//         System.setProperty("spring.autoconfigure.exclude",
//             "org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration," +
//             "org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration");
//
//         SpringApplication app = new SpringApplication(CrawlerStandaloneApplication.class);
//
//         // 设置默认配置
//         app.setDefaultProperties(java.util.Collections.singletonMap(
//             "server.port", "8081"
//         ));
//
//         app.run(args);
//
//         System.out.println("=================================");
//         System.out.println("煤炭爬虫独立服务启动成功！");
//         System.out.println("服务端口: 8081");
//         System.out.println("API基础路径: http://localhost:8081/api/coal/crawler");
//         System.out.println("健康检查: http://localhost:8081/api/coal/crawler/health");
//         System.out.println("=================================");
//     }
// }
