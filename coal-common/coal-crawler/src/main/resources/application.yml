# 煤炭爬虫模块配置
coal:
  crawler:
    # 默认超时时间（毫秒）
    default-timeout: 30000
    # 是否启用无头模式
    headless: true
    # 页面加载等待时间（毫秒）
    page-load-wait: 2000
    # 重试次数
    retry-count: 3
    # 数据更新间隔（毫秒，1小时）
    update-interval: 3600000
    # 是否启用定时任务
    enable-schedule: false
    # 定时任务cron表达式（每小时执行一次）
    schedule-cron: "0 0 */1 * * ?"

# 日志配置
logging:
  level:
    com.erdos.coal.crawler: DEBUG
    com.microsoft.playwright: WARN

spring:
  application:
    name: coal-crawler
  profiles:
    active: dev

  # 自动配置排除
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration
      - org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration

  # 数据源配置（如果需要）
  datasource:
    # 这里可以配置数据源，如果不需要可以注释掉
    # url: ***********************************
    # username: root
    # password: password