#!/bin/bash

# Java 8兼容性验证脚本

echo "=== Java 8兼容性验证 ==="

# 检查Java版本
echo "1. 检查Java版本..."
java -version

# 检查Maven版本
echo -e "\n2. 检查Maven版本..."
mvn -version

# 编译项目
echo -e "\n3. 编译项目..."
mvn clean compile

if [ $? -eq 0 ]; then
    echo "✓ 编译成功 - Java 8兼容性验证通过"
else
    echo "✗ 编译失败 - 存在Java 8兼容性问题"
    exit 1
fi

# 运行JavaScript兼容性测试
echo -e "\n4. 运行JavaScript兼容性测试..."
mvn test -Dtest=JavaScriptCompatibilityTest#testJavaScriptSyntax

if [ $? -eq 0 ]; then
    echo "✓ JavaScript语法测试通过"
else
    echo "✗ JavaScript语法测试失败"
fi

# 检查是否使用了Java 8不支持的语法
echo -e "\n5. 检查代码中是否使用了不兼容的语法..."

# 检查文本块语法
if grep -r '"""' src/; then
    echo "✗ 发现文本块语法（Java 13+特性）"
    exit 1
else
    echo "✓ 未发现文本块语法"
fi

# 检查var关键字
if grep -r '\bvar\b' src/ --include="*.java"; then
    echo "⚠ 发现var关键字（Java 10+特性），请检查是否必要"
else
    echo "✓ 未发现var关键字"
fi

# 检查switch表达式
if grep -r 'switch.*->' src/ --include="*.java"; then
    echo "⚠ 发现switch表达式（Java 14+特性），请检查是否必要"
else
    echo "✓ 未发现switch表达式"
fi

echo -e "\n=== Java 8兼容性验证完成 ==="
echo "如果所有检查都通过，说明代码完全兼容Java 8"
