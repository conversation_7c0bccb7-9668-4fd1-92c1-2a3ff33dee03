package com.erdos.coal.init.database;

import com.erdos.coal.core.utils.EntityTools;
import dev.morphia.Datastore;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;

import java.util.List;
import java.util.Set;

public class SystemConfig {

    private final static String basePackage = "com.erdos.coal.park";

    /**
     * 说明: 初始化集合
     */
    public static void initSysConfig(Datastore datastore, MongoTemplate mongoTemplate) {

        Logger logger = LoggerFactory.getLogger(SystemConfig.class);

        try {
            //TODO: 扫描包下带有 @Entity 注解的实体类
            Set<Class<?>> classSet = EntityTools.getEntityCollectionClass(basePackage);
            classSet.forEach((c) -> {
                datastore.getMapper().addMappedClass(c);
            });

            //TODO: 创建固定集合, 必须在集合创建之前， 见 ensureCaps 方法.
            datastore.ensureCaps();

            //TODO: 创建集合
            List<String> collections = EntityTools.getCollectionNamesByEntityCollectionClass(classSet);
            for (String coll : collections) {
                //1, 判断集合是否存在
                if (!mongoTemplate.collectionExists(coll)) {
                    //2, 创建集合
                    mongoTemplate.createCollection(coll);

                    logger.info("集合:" + coll + " 不存在，但已生成!");
                }
            }

            //TODO: 生成索引
            datastore.ensureIndexes();
            //或
            //classSet.forEach((c) -> {
            //    logger.info("创建索引: {}", c.getSimpleName());
            //    datastore.ensureIndexes(c);
            //});
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    //------------------------------------------------------------------------------------------------------------------

    public static void main(String[] args) throws Exception {
        String basePackage = "com.erdos.coal.park";
        Set<Class<?>> classSet = EntityTools.getEntityCollectionClass(basePackage);
        List<String> list = EntityTools.getCollectionNamesByEntityCollectionClass(classSet);
        LoggerFactory.getLogger(SystemConfig.class).info("初始化", list.toString());
    }


}
